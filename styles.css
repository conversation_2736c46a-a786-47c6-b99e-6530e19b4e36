/* Material Design 3 Golf App Styles */

/* CSS Variables for Material Design 3 Colors */
:root {
    /* Primary Colors */
    --md-primary: #4CAF50;
    --md-primary-dark: #388E3C;
    --md-primary-light: #81C784;
    
    /* Surface Colors */
    --md-surface: #FFFFFF;
    --md-surface-variant: #F5F5F5;
    --md-surface-container: #FAFAFA;
    
    /* Text Colors */
    --md-on-surface: #1C1B1F;
    --md-on-surface-variant: #49454F;
    --md-on-primary: #FFFFFF;
    
    /* Error Colors */
    --md-error: #BA1A1A;
    
    /* Elevation Shadows */
    --elevation-1: 0px 1px 3px rgba(0, 0, 0, 0.12), 0px 1px 2px rgba(0, 0, 0, 0.24);
    --elevation-2: 0px 3px 6px rgba(0, 0, 0, 0.16), 0px 3px 6px rgba(0, 0, 0, 0.23);
    --elevation-3: 0px 10px 20px rgba(0, 0, 0, 0.19), 0px 6px 6px rgba(0, 0, 0, 0.23);
    
    /* Typography */
    --font-family: 'Roboto', sans-serif;
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: var(--font-family);
    background-color: #000;
    overflow: hidden;
    height: 100vh;
    width: 100vw;
    position: relative;
}

/* Android Device Frame */
.status-bar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    height: 24px;
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
    color: white;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 16px;
    font-size: 12px;
    font-weight: 500;
    z-index: 1000;
}

.status-left .time {
    font-weight: 600;
}

.status-right {
    display: flex;
    align-items: center;
    gap: 4px;
}

.status-right .material-icons {
    font-size: 14px;
}

.battery {
    font-size: 11px;
    margin-left: 2px;
}

.navigation-bar {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    height: 32px;
    background: #000;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.nav-indicator {
    width: 134px;
    height: 4px;
    background: white;
    border-radius: 2px;
    opacity: 0.8;
}

/* App Container */
.app-container {
    position: relative;
    width: 100%;
    height: calc(100vh - 56px);
    margin-top: 24px;
    overflow: hidden;
}

/* Screen Management */
.screen {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    transform: translateX(100%);
    transition: all 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
    z-index: 1;
}

.screen.active {
    opacity: 1;
    transform: translateX(0);
    z-index: 2;
}

.screen-content {
    position: relative;
    width: 100%;
    height: 100%;
    padding: 16px;
    display: flex;
    flex-direction: column;
    z-index: 2;
}

/* Background Images */
.background-image {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-size: cover;
    background-position: center;
    z-index: 1;
}

.golf-course-bg {
    background-image: linear-gradient(rgba(0, 0, 0, 0.3), rgba(0, 0, 0, 0.3)),
                      url('https://images.unsplash.com/photo-1535131749006-b7f58c99034b?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80');
}

.golf-course-bg.blurred {
    filter: blur(2px);
    background-image: linear-gradient(rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.5)),
                      url('https://images.unsplash.com/photo-1535131749006-b7f58c99034b?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80');
}

.golf-course-bg.dark-overlay {
    background-image: linear-gradient(rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.6)),
                      url('https://images.unsplash.com/photo-1535131749006-b7f58c99034b?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80');
}

.golf-course-bg.gradient-overlay {
    background-image: linear-gradient(135deg, rgba(76, 175, 80, 0.8) 0%, rgba(0, 0, 0, 0.6) 100%),
                      url('https://images.unsplash.com/photo-1535131749006-b7f58c99034b?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80');
}

.golf-pattern-bg {
    background-image: linear-gradient(rgba(255, 255, 255, 0.95), rgba(255, 255, 255, 0.95)),
                      url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="2" fill="%234CAF50" opacity="0.1"/></svg>');
    background-size: 50px 50px;
}

.golf-sunset-bg {
    background-image: linear-gradient(rgba(255, 193, 7, 0.3), rgba(255, 87, 34, 0.3)),
                      url('https://images.unsplash.com/photo-1506905925346-21bda4d32df4?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80');
}

/* Welcome Screen */
.welcome-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    text-align: center;
}

.app-logo {
    position: relative;
    margin-bottom: 24px;
}

.app-logo .fa-golf-ball {
    font-size: 48px;
    color: white;
    filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.3));
}

.app-logo .flag-icon {
    position: absolute;
    top: -8px;
    right: -8px;
    font-size: 24px;
    color: var(--md-primary);
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
}

.app-title {
    font-size: 32px;
    font-weight: 700;
    color: white;
    margin-bottom: 32px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

.welcome-card {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    border-radius: 16px;
    padding: 24px;
    margin-bottom: 48px;
    box-shadow: var(--elevation-2);
}

.tagline {
    font-size: 16px;
    color: var(--md-on-surface-variant);
    font-weight: 400;
}

/* Material Design Components */

/* Floating Action Button */
.fab {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    background: var(--md-primary);
    color: var(--md-on-primary);
    border: none;
    border-radius: 28px;
    padding: 16px 24px;
    font-size: 16px;
    font-weight: 500;
    font-family: var(--font-family);
    box-shadow: var(--elevation-3);
    cursor: pointer;
    transition: all 0.2s cubic-bezier(0.4, 0.0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.fab:hover {
    box-shadow: var(--elevation-3), 0 0 0 8px rgba(76, 175, 80, 0.12);
    transform: translateY(-1px);
}

.fab:active {
    transform: translateY(0);
    box-shadow: var(--elevation-2);
}

.fab.primary {
    background: var(--md-primary);
}

.payment-fab {
    position: fixed;
    bottom: 80px;
    left: 50%;
    transform: translateX(-50%);
    min-width: 200px;
}

/* Cards */
.content-card, .details-card, .payment-card {
    background: var(--md-surface);
    border-radius: 16px;
    padding: 24px;
    box-shadow: var(--elevation-1);
    margin-bottom: 16px;
}

/* App Bar */
.app-bar {
    display: flex;
    align-items: center;
    padding: 16px 0;
    margin-bottom: 16px;
}

.app-bar-title {
    font-size: 22px;
    font-weight: 500;
    color: white;
    margin-left: 16px;
}

.icon-button {
    width: 48px;
    height: 48px;
    border-radius: 24px;
    background: rgba(255, 255, 255, 0.1);
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: background-color 0.2s;
}

.icon-button:hover {
    background: rgba(255, 255, 255, 0.2);
}

.icon-button .material-icons {
    color: white;
    font-size: 24px;
}

/* Text Input Layout (Material Design) */
.text-input-layout {
    position: relative;
    margin-bottom: 24px;
}

.text-input-layout input,
.text-input-layout textarea {
    width: 100%;
    padding: 16px 12px 8px 12px;
    border: none;
    outline: none;
    background: transparent;
    font-size: 16px;
    font-family: var(--font-family);
    color: var(--md-on-surface);
}

.text-input-layout textarea {
    min-height: 80px;
    resize: vertical;
}

.text-input-layout label {
    position: absolute;
    left: 12px;
    top: 16px;
    font-size: 16px;
    color: var(--md-on-surface-variant);
    transition: all 0.2s cubic-bezier(0.4, 0.0, 0.2, 1);
    pointer-events: none;
}

.text-input-layout input:focus + label,
.text-input-layout input:not(:placeholder-shown) + label,
.text-input-layout textarea:focus + label,
.text-input-layout textarea:not(:placeholder-shown) + label {
    top: 4px;
    font-size: 12px;
    color: var(--md-primary);
}

.input-line {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: var(--md-on-surface-variant);
    opacity: 0.38;
}

.input-line::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    width: 0;
    height: 2px;
    background: var(--md-primary);
    transition: all 0.2s cubic-bezier(0.4, 0.0, 0.2, 1);
    transform: translateX(-50%);
}

.text-input-layout input:focus ~ .input-line::after,
.text-input-layout textarea:focus ~ .input-line::after {
    width: 100%;
}

/* Phone Input */
.phone-input-container {
    display: flex;
    gap: 12px;
    align-items: flex-end;
}

.country-picker {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px;
    border-radius: 8px;
    background: var(--md-surface-variant);
    cursor: pointer;
    min-width: 80px;
}

.country-picker .flag {
    width: 20px;
    height: auto;
}

.country-picker span {
    font-weight: 500;
}

.phone-input-container .text-input-layout {
    flex: 1;
}

.helper-text {
    font-size: 12px;
    color: var(--md-on-surface-variant);
    margin-top: 4px;
}

/* Screen Titles */
.screen-title {
    font-size: 24px;
    font-weight: 500;
    color: var(--md-on-surface);
    margin-bottom: 24px;
}

/* Date Picker */
.date-picker {
    display: flex;
    gap: 12px;
    margin-bottom: 24px;
    overflow-x: auto;
    padding: 8px 0;
}

.date-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 12px 16px;
    border-radius: 12px;
    background: rgba(255, 255, 255, 0.1);
    color: white;
    cursor: pointer;
    transition: all 0.2s;
    min-width: 60px;
}

.date-item.selected {
    background: var(--md-primary);
    transform: scale(1.05);
}

.date-day {
    font-size: 12px;
    font-weight: 400;
    opacity: 0.8;
}

.date-number {
    font-size: 18px;
    font-weight: 500;
    margin-top: 4px;
}

/* Tee Time Cards */
.tee-times-list {
    flex: 1;
    overflow-y: auto;
}

.tee-time-card {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 12px;
    padding: 16px;
    margin-bottom: 12px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    box-shadow: var(--elevation-1);
}

.time-info {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.time {
    font-size: 18px;
    font-weight: 500;
    color: var(--md-on-surface);
}

.players {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 14px;
    color: var(--md-on-surface-variant);
}

.players .material-icons {
    font-size: 16px;
}

.price {
    font-size: 20px;
    font-weight: 600;
    color: var(--md-primary);
}

.select-button {
    background: var(--md-primary);
    color: var(--md-on-primary);
    border: none;
    border-radius: 20px;
    padding: 8px 16px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
}

.select-button:hover {
    background: var(--md-primary-dark);
    transform: translateY(-1px);
}

/* Help Banner */
.help-banner {
    display: flex;
    align-items: center;
    gap: 8px;
    background: rgba(255, 255, 255, 0.1);
    color: white;
    padding: 12px 16px;
    border-radius: 8px;
    margin-top: auto;
    cursor: pointer;
    transition: background-color 0.2s;
}

.help-banner:hover {
    background: rgba(255, 255, 255, 0.2);
}

/* Detail Items */
.detail-item {
    margin-bottom: 20px;
}

.detail-item label {
    display: block;
    font-size: 12px;
    font-weight: 500;
    color: var(--md-on-surface-variant);
    margin-bottom: 8px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.detail-value {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 16px;
    color: var(--md-on-surface);
}

.edit-icon {
    color: var(--md-on-surface-variant);
    cursor: pointer;
    transition: color 0.2s;
}

.edit-icon:hover {
    color: var(--md-primary);
}

/* Action Buttons */
.action-buttons {
    display: flex;
    gap: 12px;
    margin-top: auto;
    padding-top: 24px;
}

.button {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    padding: 12px 24px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: 500;
    font-family: var(--font-family);
    cursor: pointer;
    transition: all 0.2s;
}

.button.outlined {
    background: transparent;
    border: 1px solid var(--md-primary);
    color: var(--md-primary);
}

.button.outlined:hover {
    background: rgba(76, 175, 80, 0.08);
}

.button.contained {
    background: var(--md-primary);
    border: none;
    color: var(--md-on-primary);
    box-shadow: var(--elevation-1);
}

.button.contained:hover {
    background: var(--md-primary-dark);
    box-shadow: var(--elevation-2);
}

/* Payment Screen */
.payment-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 24px;
}

.payment-header h3 {
    font-size: 20px;
    font-weight: 500;
    color: var(--md-on-surface);
}

.security-icon {
    color: var(--md-primary);
    font-size: 24px;
}

.card-preview {
    background: linear-gradient(135deg, var(--md-primary) 0%, var(--md-primary-dark) 100%);
    color: white;
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 24px;
    position: relative;
    overflow: hidden;
}

.card-preview::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
}

.card-number {
    font-size: 18px;
    font-weight: 500;
    letter-spacing: 2px;
    margin-bottom: 16px;
}

.card-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 14px;
}

.card-brand {
    position: absolute;
    top: 16px;
    right: 16px;
    font-size: 24px;
}

.input-row {
    display: flex;
    gap: 16px;
}

.input-row .text-input-layout {
    flex: 1;
}

.payment-footer {
    position: fixed;
    bottom: 140px;
    left: 50%;
    transform: translateX(-50%);
    text-align: center;
}

.payment-logos {
    display: flex;
    gap: 16px;
    justify-content: center;
    margin-bottom: 8px;
}

.payment-logos i {
    font-size: 32px;
    color: var(--md-on-surface-variant);
}

.secure-text {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 12px;
    color: var(--md-on-surface-variant);
}

/* Success Screen */
.success-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    text-align: center;
}

.success-animation {
    margin-bottom: 32px;
}

.checkmark-circle {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: var(--md-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    animation: checkmarkPulse 0.6s ease-out;
}

.checkmark {
    color: white;
    font-size: 40px;
    animation: checkmarkScale 0.3s ease-out 0.3s both;
}

@keyframes checkmarkPulse {
    0% {
        transform: scale(0);
        opacity: 0;
    }
    50% {
        transform: scale(1.1);
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

@keyframes checkmarkScale {
    0% {
        transform: scale(0);
    }
    100% {
        transform: scale(1);
    }
}

.success-title {
    font-size: 28px;
    font-weight: 600;
    color: white;
    margin-bottom: 32px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.booking-summary {
    background: rgba(255, 255, 255, 0.9);
    border-radius: 16px;
    padding: 24px;
    margin-bottom: 48px;
    box-shadow: var(--elevation-2);
}

.summary-item {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 12px;
    font-size: 16px;
    color: var(--md-on-surface);
}

.summary-item:last-child {
    margin-bottom: 0;
}

.summary-item .material-icons {
    color: var(--md-primary);
    font-size: 20px;
}

/* Snackbar */
.snackbar {
    position: fixed;
    bottom: 100px;
    left: 16px;
    right: 16px;
    background: #323232;
    color: white;
    padding: 14px 16px;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    box-shadow: var(--elevation-3);
    transform: translateY(100px);
    opacity: 0;
    transition: all 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
}

.snackbar.show {
    transform: translateY(0);
    opacity: 1;
}

.snackbar-action {
    background: none;
    border: none;
    color: var(--md-primary-light);
    font-weight: 500;
    cursor: pointer;
    padding: 4px 8px;
    border-radius: 4px;
    transition: background-color 0.2s;
}

.snackbar-action:hover {
    background: rgba(129, 199, 132, 0.12);
}

/* Responsive Design */
@media (max-width: 480px) {
    .screen-content {
        padding: 12px;
    }
    
    .app-title {
        font-size: 28px;
    }
    
    .welcome-card {
        padding: 20px;
    }
    
    .content-card, .details-card, .payment-card {
        padding: 20px;
    }
    
    .action-buttons {
        flex-direction: column;
    }
    
    .button {
        width: 100%;
    }
}

/* Ripple Effect */
.fab::before,
.button::before,
.select-button::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
    transform: translate(-50%, -50%);
    transition: width 0.3s, height 0.3s;
}

.fab:active::before,
.button:active::before,
.select-button:active::before {
    width: 200px;
    height: 200px;
}

/* Loading States */
.loading {
    position: relative;
    overflow: hidden;
}

.loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
    0% {
        left: -100%;
    }
    100% {
        left: 100%;
    }
}

/* Focus States for Accessibility */
.fab:focus,
.button:focus,
.icon-button:focus,
.select-button:focus {
    outline: 2px solid var(--md-primary);
    outline-offset: 2px;
}

.text-input-layout input:focus,
.text-input-layout textarea:focus {
    outline: none;
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
    :root {
        --md-primary: #2E7D32;
        --md-on-surface: #000000;
        --md-on-surface-variant: #424242;
    }
    
    .background-image {
        filter: contrast(1.2);
    }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
} 
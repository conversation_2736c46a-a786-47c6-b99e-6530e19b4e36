/* Enhanced Golf App Styles - Modern UI Improvements with Touch-Friendly Design */

/* Touch-Friendly Base Styles */
* {
    -webkit-tap-highlight-color: rgba(76, 175, 80, 0.2);
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

input, textarea {
    -webkit-user-select: text;
    -moz-user-select: text;
    -ms-user-select: text;
    user-select: text;
}

/* App Header with Logo */
.app-header {
    position: fixed;
    top: 24px; /* Below status bar */
    left: 0;
    right: 0;
    z-index: 1000;
    padding: 8px 16px;
    pointer-events: none; /* Allow touches to pass through to content below */
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

/* Show header logo on all screens except welcome screen */
body:not(.welcome-active) .app-header {
    opacity: 1;
    visibility: visible;
}

.header-logo {
    display: flex;
    justify-content: flex-start;
    pointer-events: auto; /* Enable touches on logo */
}

.header-logo-img {
    width: 40px;
    height: 40px;
    object-fit: contain;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
    transition: all 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
    cursor: pointer;
    /* Touch improvements */
    touch-action: manipulation;
    -webkit-tap-highlight-color: transparent;
}

.header-logo-img:hover {
    transform: scale(1.1);
    filter: drop-shadow(0 3px 6px rgba(0, 0, 0, 0.4));
}

.header-logo-img:active {
    transform: scale(0.95);
}

/* Club Logo Styles (for welcome screen) */
.club-logo {
    width: 120px;
    height: 120px;
    object-fit: contain;
    filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.3));
    transition: all 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
}

.club-logo:hover {
    transform: scale(1.05);
    filter: drop-shadow(0 6px 12px rgba(0, 0, 0, 0.4));
}

/* Responsive logo sizing */
@media (max-width: 480px) {
    .app-header {
        padding: 6px 12px;
    }
    
    .header-logo-img {
        width: 36px;
        height: 36px;
    }
    
    .club-logo {
        width: 100px;
        height: 100px;
    }
}

@media (max-width: 320px) {
    .header-logo-img {
        width: 32px;
        height: 32px;
    }
    
    .club-logo {
        width: 80px;
        height: 80px;
    }
}

/* App Container Adjustments for Header Logo */
.app-container {
    padding-top: 56px; /* Space for header logo */
}

/* Enhanced Card Variants with Touch Improvements */
.enhanced-card {
    position: relative;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 16px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 
        0 8px 32px rgba(0, 0, 0, 0.1),
        0 1px 0 rgba(255, 255, 255, 0.5) inset;
    transition: all 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
    overflow: hidden;
    /* Touch improvements */
    cursor: pointer;
    -webkit-transform: translateZ(0);
    transform: translateZ(0);
}

.enhanced-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(76, 175, 80, 0.5), transparent);
}

.enhanced-card:hover {
    transform: translateY(-4px);
    box-shadow: 
        0 20px 40px rgba(0, 0, 0, 0.15),
        0 1px 0 rgba(255, 255, 255, 0.6) inset;
}

/* Premium Tee Time Cards with Touch Enhancements */
.premium-tee-card {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.95) 100%);
    border: 1px solid rgba(76, 175, 80, 0.2);
    border-radius: 20px;
    padding: 32px 24px; /* Increased vertical padding */
    margin: 0 auto 20px auto; /* Center the cards and add bottom margin */
    max-width: 380px; /* Limit the maximum width */
    width: 100%; /* Allow responsive scaling */
    position: relative;
    overflow: hidden;
    transition: all 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
    backdrop-filter: blur(20px);
    /* Touch improvements */
    cursor: pointer;
    -webkit-transform: translateZ(0);
    transform: translateZ(0);
    touch-action: manipulation;
    min-height: 160px; /* Ensure minimum touch target size */
}

.premium-tee-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--md-primary), var(--md-primary-light));
    opacity: 0;
    transition: opacity 0.3s ease;
}

.premium-tee-card:hover::before {
    opacity: 1;
}

.premium-tee-card:hover {
    transform: translateY(-2px);
    box-shadow: 
        0 12px 24px rgba(76, 175, 80, 0.15),
        0 4px 8px rgba(0, 0, 0, 0.1);
    border-color: var(--md-primary);
}

.tee-card-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 16px;
}

.tee-time-display {
    display: flex;
    flex-direction: row;
    align-items: baseline;
    gap: 8px;
}

.tee-time-main {
    font-size: 24px;
    font-weight: 700;
    color: var(--md-on-surface);
    line-height: 1;
}

.tee-time-period {
    font-size: 14px;
    font-weight: 500;
    color: var(--md-on-surface-variant);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.availability-badge {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.availability-badge.available {
    background: rgba(34, 197, 94, 0.1);
    color: #059669;
    border: 1px solid rgba(34, 197, 94, 0.2);
}

.availability-badge.limited {
    background: rgba(245, 158, 11, 0.1);
    color: #d97706;
    border: 1px solid rgba(245, 158, 11, 0.2);
}

.availability-badge.full {
    background: rgba(239, 68, 68, 0.1);
    color: #dc2626;
    border: 1px solid rgba(239, 68, 68, 0.2);
}

.availability-dot {
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background: currentColor;
}

.tee-card-details {
    display: flex;
    flex-direction: column;
    gap: 16px;
    margin-bottom: 20px;
}

.tee-info-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.tee-info-item {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    color: var(--md-on-surface-variant);
}

.tee-info-item .material-icons {
    font-size: 18px;
    color: var(--md-primary);
}

/* Player Selection Interface */
.player-selection {
    display: flex;
    flex-direction: column;
    gap: 12px;
    margin-top: 8px;
}

.player-selection-label {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    color: var(--md-on-surface-variant);
    font-weight: 500;
}

.player-selection-label .material-icons {
    font-size: 18px;
    color: var(--md-primary);
}

.player-count-buttons {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
    justify-content: center;
}

.player-count-btn {
    width: 44px;
    height: 44px;
    border: 2px solid rgba(76, 175, 80, 0.3);
    border-radius: 12px;
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10px);
    color: var(--md-on-surface);
    font-size: 16px;
    font-weight: 600;
    font-family: var(--font-family);
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
    display: flex;
    align-items: center;
    justify-content: center;
    /* Touch improvements */
    touch-action: manipulation;
    -webkit-tap-highlight-color: transparent;
    min-width: 44px;
    min-height: 44px;
}

.player-count-btn:hover {
    background: rgba(76, 175, 80, 0.1);
    border-color: var(--md-primary);
    transform: scale(1.05);
}

.player-count-btn.selected {
    background: var(--md-primary);
    border-color: var(--md-primary);
    color: white;
    box-shadow: 0 4px 8px rgba(76, 175, 80, 0.3);
}

.player-count-btn:active {
    transform: scale(0.95);
}

.price-display {
    text-align: right;
}

.price-main {
    font-size: 32px;
    font-weight: 800;
    color: var(--md-primary);
    line-height: 1;
}

.price-label {
    font-size: 12px;
    color: var(--md-on-surface-variant);
    margin-top: 2px;
}

.enhanced-select-button {
    width: 100%;
    background: linear-gradient(135deg, var(--md-primary) 0%, var(--md-primary-dark) 100%);
    color: white;
    border: none;
    border-radius: 12px;
    padding: 20px 24px; /* Increased padding for better touch target */
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
    position: relative;
    overflow: hidden;
    /* Touch improvements */
    min-height: 60px; /* Minimum 60px touch target */
    touch-action: manipulation;
    -webkit-tap-highlight-color: transparent;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.enhanced-select-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.enhanced-select-button:hover::before {
    left: 100%;
}

.enhanced-select-button:hover {
    transform: translateY(-1px);
    box-shadow: 0 8px 16px rgba(76, 175, 80, 0.3);
}

.enhanced-select-button:active {
    transform: translateY(0);
}

.enhanced-select-button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    background: linear-gradient(135deg, #9e9e9e 0%, #757575 100%);
    pointer-events: none;
    transform: none;
    box-shadow: none;
}

.enhanced-select-button:disabled::before {
    display: none;
}

/* Current Date Display */
.current-date-display {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 32px;
    padding: 20px 16px;
}

.current-date-text {
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(10px);
    border: 2px solid rgba(255, 255, 255, 0.2);
    border-radius: 16px;
    padding: 16px 32px;
    color: white;
    font-size: 18px;
    font-weight: 600;
    text-align: center;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    position: relative;
    overflow: hidden;
    /* Touch improvements */
    min-height: 56px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.current-date-text::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
    opacity: 0.5;
}

/* Enhanced Payment Card */
.enhanced-payment-card {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.95) 100%);
    border: 1px solid rgba(76, 175, 80, 0.2);
    border-radius: 24px;
    padding: 32px;
    backdrop-filter: blur(20px);
    box-shadow: 
        0 20px 40px rgba(0, 0, 0, 0.1),
        0 1px 0 rgba(255, 255, 255, 0.5) inset;
}

.enhanced-card-preview {
    background: linear-gradient(135deg, var(--md-primary) 0%, var(--md-primary-dark) 100%);
    border-radius: 16px;
    padding: 24px;
    margin-bottom: 32px;
    position: relative;
    overflow: hidden;
    box-shadow: 0 12px 24px rgba(76, 175, 80, 0.3);
}

.enhanced-card-preview::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.15) 0%, transparent 70%);
}

.enhanced-card-preview::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
}

/* Enhanced Input Fields with Touch Improvements */
.enhanced-input-group {
    position: relative;
    margin-bottom: 28px; /* Increased margin for better spacing */
}

.enhanced-input {
    width: 100%;
    padding: 20px 24px; /* Increased padding for better touch target */
    border: 2px solid rgba(76, 175, 80, 0.2);
    border-radius: 12px;
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10px);
    font-size: 16px; /* Minimum 16px to prevent zoom on iOS */
    font-family: var(--font-family);
    color: var(--md-on-surface);
    transition: all 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
    /* Touch improvements */
    min-height: 56px; /* Minimum touch target height */
    touch-action: manipulation;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
}

.enhanced-input:focus {
    outline: none;
    border-color: var(--md-primary);
    background: rgba(255, 255, 255, 0.95);
    box-shadow: 0 0 0 4px rgba(76, 175, 80, 0.1);
}

.enhanced-input-label {
    position: absolute;
    left: 24px; /* Adjusted for new padding */
    top: 20px; /* Adjusted for new padding */
    font-size: 16px;
    color: var(--md-on-surface-variant);
    transition: all 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
    pointer-events: none;
    background: linear-gradient(to bottom, transparent 40%, rgba(255, 255, 255, 0.8) 40%);
    padding: 0 4px;
    /* Touch improvements */
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

.enhanced-input:focus + .enhanced-input-label,
.enhanced-input:not(:placeholder-shown) + .enhanced-input-label {
    top: -8px;
    font-size: 12px;
    color: var(--md-primary);
    font-weight: 600;
}

/* Enhanced Success Animation */
.enhanced-success-animation {
    margin-bottom: 40px;
    position: relative;
}

.enhanced-checkmark-circle {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--md-primary) 0%, var(--md-primary-light) 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    animation: enhancedCheckmarkPulse 0.8s ease-out;
    box-shadow: 
        0 0 0 0 rgba(76, 175, 80, 0.4),
        0 8px 16px rgba(76, 175, 80, 0.3);
    position: relative;
}

.enhanced-checkmark-circle::before {
    content: '';
    position: absolute;
    top: -4px;
    left: -4px;
    right: -4px;
    bottom: -4px;
    border-radius: 50%;
    border: 2px solid var(--md-primary);
    opacity: 0.3;
    animation: enhancedRipple 2s infinite;
}

.enhanced-checkmark {
    color: white;
    font-size: 48px;
    animation: enhancedCheckmarkScale 0.4s ease-out 0.4s both;
}

@keyframes enhancedCheckmarkPulse {
    0% {
        transform: scale(0);
        opacity: 0;
        box-shadow: 0 0 0 0 rgba(76, 175, 80, 0.4);
    }
    50% {
        transform: scale(1.1);
        box-shadow: 0 0 0 20px rgba(76, 175, 80, 0);
    }
    100% {
        transform: scale(1);
        opacity: 1;
        box-shadow: 0 0 0 0 rgba(76, 175, 80, 0);
    }
}

@keyframes enhancedCheckmarkScale {
    0% {
        transform: scale(0) rotate(-45deg);
    }
    50% {
        transform: scale(1.2) rotate(-45deg);
    }
    100% {
        transform: scale(1) rotate(0deg);
    }
}

@keyframes enhancedRipple {
    0% {
        transform: scale(1);
        opacity: 0.3;
    }
    100% {
        transform: scale(1.5);
        opacity: 0;
    }
}

/* Enhanced Booking Summary */
.enhanced-booking-summary {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.95) 100%);
    border: 1px solid rgba(76, 175, 80, 0.2);
    border-radius: 20px;
    padding: 32px;
    margin-bottom: 48px;
    backdrop-filter: blur(20px);
    box-shadow: 
        0 12px 24px rgba(0, 0, 0, 0.1),
        0 1px 0 rgba(255, 255, 255, 0.5) inset;
}

.enhanced-summary-item {
    display: flex;
    align-items: center;
    gap: 16px;
    margin-bottom: 16px;
    padding: 12px 0;
    border-bottom: 1px solid rgba(76, 175, 80, 0.1);
    font-size: 16px;
    color: var(--md-on-surface);
}

.enhanced-summary-item:last-child {
    margin-bottom: 0;
    border-bottom: none;
}

.enhanced-summary-icon {
    width: 40px;
    height: 40px;
    border-radius: 10px;
    background: linear-gradient(135deg, var(--md-primary) 0%, var(--md-primary-light) 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 20px;
}

/* Enhanced Floating Action Button with Touch Improvements */
.enhanced-fab {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
    background: linear-gradient(135deg, var(--md-primary) 0%, var(--md-primary-dark) 100%);
    color: white;
    border: none;
    border-radius: 28px;
    padding: 22px 36px; /* Increased padding for better touch target */
    font-size: 16px;
    font-weight: 600;
    font-family: var(--font-family);
    box-shadow: 
        0 8px 16px rgba(76, 175, 80, 0.3),
        0 1px 0 rgba(255, 255, 255, 0.2) inset;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
    position: relative;
    overflow: hidden;
    min-width: 180px; /* Increased minimum width */
    min-height: 64px; /* Minimum touch target height */
    /* Touch improvements */
    touch-action: manipulation;
    -webkit-tap-highlight-color: transparent;
    -webkit-transform: translateZ(0);
    transform: translateZ(0);
}

.enhanced-fab::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.6s ease;
}

.enhanced-fab:hover {
    transform: translateY(-2px);
    box-shadow: 
        0 12px 24px rgba(76, 175, 80, 0.4),
        0 1px 0 rgba(255, 255, 255, 0.3) inset;
}

.enhanced-fab:hover::before {
    left: 100%;
}

.enhanced-fab:active {
    transform: translateY(0);
    box-shadow: 
        0 4px 8px rgba(76, 175, 80, 0.3),
        0 1px 0 rgba(255, 255, 255, 0.2) inset;
}

/* Enhanced Status Bar */
.enhanced-status-bar {
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 50%, #1a1a1a 100%);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

/* Enhanced Navigation Bar */
.enhanced-nav-bar {
    background: linear-gradient(135deg, #000 0%, #1a1a1a 100%);
    box-shadow: 0 -1px 3px rgba(0, 0, 0, 0.3);
}

.enhanced-nav-indicator {
    background: linear-gradient(90deg, rgba(255, 255, 255, 0.6), rgba(255, 255, 255, 0.9), rgba(255, 255, 255, 0.6));
    box-shadow: 0 0 8px rgba(255, 255, 255, 0.3);
}

/* Enhanced Snackbar */
.enhanced-snackbar {
    background: linear-gradient(135deg, #2d3748 0%, #1a202c 100%);
    border: 1px solid rgba(76, 175, 80, 0.3);
    border-radius: 12px;
    box-shadow: 
        0 12px 24px rgba(0, 0, 0, 0.3),
        0 1px 0 rgba(255, 255, 255, 0.1) inset;
    backdrop-filter: blur(20px);
}

/* Touch-Friendly Responsive Enhancements */
@media (max-width: 480px) {
    .premium-tee-card {
        padding: 28px 20px; /* Increased vertical padding for touch */
        border-radius: 16px;
        margin: 0 auto 24px auto; /* Center and increase spacing */
        max-width: 340px; /* Slightly smaller on mobile */
    }
    
    .enhanced-payment-card {
        padding: 28px 20px; /* Increased vertical padding */
        border-radius: 20px;
    }
    
    .enhanced-booking-summary {
        padding: 28px 20px; /* Increased vertical padding */
        border-radius: 16px;
    }
    
    .tee-time-main {
        font-size: 20px;
    }
    
    .price-main {
        font-size: 28px;
    }
    
    /* Touch-specific improvements for mobile */
    .enhanced-select-button {
        padding: 24px; /* Larger touch target on mobile */
        min-height: 68px;
        font-size: 16px;
    }
    
    .enhanced-fab {
        padding: 24px 40px; /* Larger touch target */
        min-height: 68px;
        min-width: 200px;
    }
    
    .current-date-text {
        padding: 14px 24px; /* Adjusted for mobile */
        font-size: 16px;
        min-height: 52px;
    }
    
    .player-count-btn {
        width: 40px; /* Slightly smaller on mobile but still touch-friendly */
        height: 40px;
        font-size: 14px;
        min-width: 40px;
        min-height: 40px;
    }
    
    .player-selection-label {
        font-size: 13px;
    }
    
    .code-digit {
        width: 60px; /* Larger on mobile */
        height: 68px;
        font-size: 26px;
    }
    
    .verification-code-input {
        gap: 20px; /* More spacing on mobile */
    }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    .enhanced-card,
    .premium-tee-card,
    .enhanced-payment-card,
    .enhanced-booking-summary {
        background: linear-gradient(135deg, rgba(30, 41, 59, 0.95) 0%, rgba(15, 23, 42, 0.95) 100%);
        border-color: rgba(76, 175, 80, 0.3);
    }
    
    .enhanced-input {
        background: rgba(30, 41, 59, 0.8);
        border-color: rgba(76, 175, 80, 0.3);
        color: #e2e8f0;
    }
    
    .enhanced-input:focus {
        background: rgba(30, 41, 59, 0.95);
    }
    
    .current-date-text {
        background: rgba(30, 41, 59, 0.8);
        border-color: rgba(76, 175, 80, 0.3);
        color: #e2e8f0;
    }
    
    .player-count-btn {
        background: rgba(30, 41, 59, 0.8);
        border-color: rgba(76, 175, 80, 0.3);
        color: #e2e8f0;
    }
    
    .player-count-btn:hover {
        background: rgba(76, 175, 80, 0.2);
    }
    
    .player-count-btn.selected {
        background: var(--md-primary);
        color: white;
    }
}

/* Enhanced Verification Screen Styles */
.verification-container {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: calc(100vh - 200px);
    padding: 20px;
}

.verification-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 24px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 
        0 20px 40px rgba(0, 0, 0, 0.1),
        0 1px 0 rgba(255, 255, 255, 0.5) inset;
    text-align: center;
    padding: 40px 32px;
    max-width: 420px;
    width: 100%;
    position: relative;
    overflow: hidden;
}

.verification-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--md-primary) 0%, var(--md-primary-light) 50%, var(--md-primary) 100%);
}

.verification-header {
    margin-bottom: 40px;
}

.verification-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--md-primary) 0%, var(--md-primary-light) 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 24px;
    box-shadow: 0 8px 16px rgba(76, 175, 80, 0.3);
}

.verification-icon .material-icons {
    font-size: 36px;
    color: white;
}

.verification-subtitle {
    font-size: 16px;
    color: var(--md-on-surface-variant);
    line-height: 1.5;
    margin-top: 12px;
}

.verification-subtitle strong {
    color: var(--md-primary);
    font-weight: 600;
}

/* Code Input Styling with Touch Improvements */
.verification-code-input {
    display: flex;
    gap: 12px; /* Reduced gap to fit better in narrower card */
    justify-content: center;
    margin-bottom: 32px;
    padding: 0 16px;
    /* Touch improvements */
    -webkit-overflow-scrolling: touch;
}

.code-digit {
    width: 48px; /* Reduced width to fit better in narrower card */
    height: 56px; /* Reduced height proportionally */
    border: 2px solid rgba(76, 175, 80, 0.2);
    border-radius: 12px;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    text-align: center;
    font-size: 22px; /* Slightly smaller font */
    font-weight: 700;
    font-family: var(--font-family);
    color: var(--md-on-surface);
    transition: all 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
    outline: none;
    /* Touch improvements */
    touch-action: manipulation;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    flex-shrink: 0; /* Prevent shrinking on mobile */
}

.code-digit:focus {
    border-color: var(--md-primary);
    background: rgba(255, 255, 255, 0.95);
    box-shadow: 0 0 0 4px rgba(76, 175, 80, 0.1);
    transform: scale(1.05);
}

.code-digit:not(:placeholder-shown) {
    border-color: var(--md-primary);
    background: rgba(76, 175, 80, 0.05);
}

.code-digit.error {
    border-color: var(--md-error);
    background: rgba(186, 26, 26, 0.05);
    animation: shakeError 0.5s ease-in-out;
}

@keyframes shakeError {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-4px); }
    75% { transform: translateX(4px); }
}

/* Verification Timer */
.verification-timer {
    margin-bottom: 32px;
}

.timer-text {
    font-size: 14px;
    color: var(--md-on-surface-variant);
    margin-bottom: 16px;
}

.timer-text span {
    color: var(--md-primary);
    font-weight: 600;
}

.resend-button {
    display: flex;
    align-items: center;
    gap: 8px;
    background: transparent;
    border: 2px solid rgba(76, 175, 80, 0.3);
    border-radius: 12px;
    padding: 16px 24px; /* Increased padding for better touch target */
    color: var(--md-primary);
    font-size: 14px;
    font-weight: 600;
    font-family: var(--font-family);
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
    margin: 0 auto;
    /* Touch improvements */
    min-height: 52px; /* Minimum touch target height */
    touch-action: manipulation;
    -webkit-tap-highlight-color: transparent;
}

.resend-button:disabled {
    border-color: rgba(0, 0, 0, 0.12);
    color: rgba(0, 0, 0, 0.38);
    cursor: not-allowed;
}

.resend-button:not(:disabled):hover {
    background: rgba(76, 175, 80, 0.08);
    border-color: var(--md-primary);
    transform: translateY(-1px);
}

.resend-button .material-icons {
    font-size: 18px;
}

/* Verification FAB */
.verification-fab {
    opacity: 0.5;
    cursor: not-allowed;
    transition: all 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
    display: block;
    margin: 32px auto 0 auto;
    position: relative;
}

.verification-fab:not(:disabled) {
    opacity: 1;
    cursor: pointer;
}

.verification-fab:not(:disabled):hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 24px rgba(76, 175, 80, 0.4);
}

/* Success Animation for Valid Code */
.code-success {
    border-color: #4CAF50 !important;
    background: rgba(76, 175, 80, 0.1) !important;
    animation: pulseSuccess 0.3s ease-in-out;
}

@keyframes pulseSuccess {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

/* Touch-Friendly App Bar and Navigation */
.app-bar {
    padding: 16px 20px; /* Increased padding */
}

.icon-button {
    width: 48px; /* Increased from default */
    height: 48px; /* Increased from default */
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: none;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
    /* Touch improvements */
    touch-action: manipulation;
    -webkit-tap-highlight-color: transparent;
}

.icon-button:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: scale(1.05);
}

.icon-button .material-icons {
    color: white;
    font-size: 24px;
}

/* Touch-Friendly Help Banner */
.help-banner {
    padding: 20px 24px; /* Increased padding */
    margin: 24px 0; /* Increased margin */
    border-radius: 16px;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    display: flex;
    align-items: center;
    gap: 12px;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
    /* Touch improvements */
    min-height: 60px;
    touch-action: manipulation;
    -webkit-tap-highlight-color: transparent;
}

.help-banner:hover {
    background: rgba(255, 255, 255, 0.15);
    transform: translateY(-1px);
}

/* Responsive Design for Verification - Updated for Touch */
@media (max-width: 480px) {
    .verification-container {
        min-height: calc(100vh - 160px);
        padding: 16px;
        align-items: flex-start;
        padding-top: 60px;
    }
    
    .verification-card {
        max-width: 100%; /* Full width on mobile */
        padding: 32px 24px;
        border-radius: 20px;
    }
    
    .verification-code-input {
        gap: 10px; /* Smaller gap for mobile to fit in narrower card */
        padding: 0 12px;
    }
    
    .code-digit {
        width: 44px; /* Smaller to fit in mobile card */
        height: 52px; /* Proportionally smaller */
        font-size: 20px; /* Smaller font for mobile */
    }
    
    .verification-icon {
        width: 64px;
        height: 64px;
    }
    
    .verification-icon .material-icons {
        font-size: 28px;
    }
    
    .icon-button {
        width: 44px; /* Slightly smaller on mobile but still touch-friendly */
        height: 44px;
    }
}

/* Dark mode support for verification */
@media (prefers-color-scheme: dark) {
    .verification-card {
        background: linear-gradient(135deg, rgba(30, 41, 59, 0.95) 0%, rgba(15, 23, 42, 0.95) 100%);
        border-color: rgba(76, 175, 80, 0.3);
    }
    
    .code-digit {
        background: rgba(30, 41, 59, 0.8);
        border-color: rgba(76, 175, 80, 0.3);
        color: #e2e8f0;
    }
    
    .code-digit:focus {
        background: rgba(30, 41, 59, 0.95);
    }
}

/* Enhanced Booking Details Screen Styles */

/* Proceed Button */
.proceed-button {
    width: 100%;
    max-width: 400px;
    margin: 24px auto 0;
    padding: 16px;
    font-size: 16px;
    font-weight: 600;
    color: white;
    background: linear-gradient(135deg, var(--md-primary), var(--md-primary-dark));
    border: none;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    box-shadow: 0 4px 12px rgba(76, 175, 80, 0.25);
    transition: all 0.2s ease;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    touch-action: manipulation;
    -webkit-tap-highlight-color: transparent;
}

.proceed-button::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.15), transparent);
    opacity: 0;
    transition: opacity 0.2s ease;
}

.proceed-button:active {
    transform: translateY(2px);
    box-shadow: 0 2px 8px rgba(76, 175, 80, 0.2);
}

.proceed-button:active::after {
    opacity: 1;
}

.proceed-button .material-icons {
    font-size: 20px;
    transition: transform 0.2s ease;
}

.proceed-button:active .material-icons {
    transform: translateX(2px);
}

/* Booking Details Container */
.booking-details-container {
    max-width: 500px;
    margin: 0 auto;
    width: 100%;
}

/* Tee Time Summary */
.tee-time-summary {
    margin-bottom: 24px;
    max-width: 500px;
    margin-left: auto;
    margin-right: auto;
}

.summary-header {
    display: flex;
    align-items: center;
    gap: 16px;
    padding: 20px;
}

.summary-icon {
    width: 48px;
    height: 48px;
    background: linear-gradient(135deg, var(--md-primary) 0%, var(--md-primary-light) 100%);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.summary-icon .material-icons {
    color: white;
    font-size: 24px;
}

.summary-info {
    flex: 1;
}

.summary-info h3 {
    font-size: 18px;
    font-weight: 600;
    color: var(--md-on-surface);
    margin: 0 0 4px 0;
}

.summary-info p {
    font-size: 14px;
    color: var(--md-on-surface-variant);
    margin: 0;
}

.edit-button {
    width: 48px; /* Increased for better touch target */
    height: 48px; /* Increased for better touch target */
    background: transparent;
    border: 2px solid rgba(76, 175, 80, 0.3);
    border-radius: 12px; /* Slightly larger radius */
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
    /* Touch improvements */
    touch-action: manipulation;
    -webkit-tap-highlight-color: transparent;
}

.edit-button:hover {
    background: rgba(76, 175, 80, 0.08);
    border-color: var(--md-primary);
    transform: scale(1.05);
}

.edit-button .material-icons {
    color: var(--md-primary);
    font-size: 20px;
}

/* Players Section */
.players-section {
    margin-bottom: 24px;
    max-width: 500px;
    margin-left: auto;
    margin-right: auto;
}

.section-title {
    font-size: 22px;
    font-weight: 700;
    color: var(--md-on-surface);
    margin: 0 0 24px 0;
    padding: 0 8px 12px;
    position: relative;
    display: inline-block;
    letter-spacing: -0.3px;
    line-height: 1.3;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 8px;
    width: 60px;
    height: 4px;
    background: linear-gradient(90deg, var(--md-primary), var(--md-primary-light));
    border-radius: 2px;
}

/* Animation for section title */
@keyframes titleUnderline {
    0% { width: 0; opacity: 0; }
    100% { width: 60px; opacity: 1; }
}

.section-title.animate::after {
    animation: titleUnderline 0.6s ease-out forwards;
}

/* Player Cards */
.player-card {
    margin-bottom: 16px;
    padding: 0;
    overflow: hidden;
}

.player-header {
    display: flex;
    align-items: center;
    gap: 16px;
    padding: 20px 24px 16px;
    background: linear-gradient(135deg, rgba(76, 175, 80, 0.05) 0%, rgba(76, 175, 80, 0.02) 100%);
    border-bottom: 1px solid rgba(76, 175, 80, 0.1);
}

.player-avatar {
    width: 48px;
    height: 48px;
    background: linear-gradient(135deg, var(--md-primary) 0%, var(--md-primary-light) 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.player-avatar .material-icons {
    color: white;
    font-size: 24px;
}

.player-info {
    flex: 1;
}

.player-info h4 {
    font-size: 16px;
    font-weight: 600;
    color: var(--md-on-surface);
    margin: 0 0 2px 0;
}

.player-info p {
    font-size: 14px;
    color: var(--md-on-surface-variant);
    margin: 0;
}

.player-price {
    text-align: right;
}

.player-price .price {
    font-size: 18px;
    font-weight: 700;
    color: var(--md-primary);
}

.player-details {
    padding: 20px 24px 24px;
    display: flex;
    flex-direction: column;
    gap: 20px;
}

/* Add Player Button */
.add-player-button {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    width: 100%;
    padding: 20px 16px; /* Increased padding for better touch target */
    background: transparent;
    border: 2px dashed rgba(76, 175, 80, 0.3);
    border-radius: 16px;
    color: var(--md-primary);
    font-size: 14px;
    font-weight: 600;
    font-family: var(--font-family);
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
    margin-bottom: 12px; /* Increased margin */
    /* Touch improvements */
    min-height: 60px; /* Minimum touch target height */
    touch-action: manipulation;
    -webkit-tap-highlight-color: transparent;
}

.add-player-button:hover {
    background: rgba(76, 175, 80, 0.05);
    border-color: var(--md-primary);
    transform: translateY(-1px);
}

.add-player-button .material-icons {
    font-size: 20px;
}

/* Special Requests */
.special-requests {
    margin-bottom: 24px;
    padding: 24px;
    max-width: 500px;
    margin-left: auto;
    margin-right: auto;
}

.special-requests h3 {
    font-size: 18px;
    font-weight: 600;
    color: var(--md-on-surface);
    margin: 0 0 20px 0;
}

.notes-textarea {
    min-height: 80px;
    resize: vertical;
    padding-top: 16px;
}

/* Cost Breakdown */
.cost-breakdown {
    margin-bottom: 32px;
    padding: 24px;
    max-width: 500px;
    margin-left: auto;
    margin-right: auto;
}

.cost-breakdown h3 {
    font-size: 18px;
    font-weight: 600;
    color: var(--md-on-surface);
    margin: 0 0 20px 0;
}

.cost-items {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.cost-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.cost-label {
    font-size: 14px;
    color: var(--md-on-surface-variant);
    font-weight: 500;
}

.cost-value {
    font-size: 16px;
    font-weight: 600;
    color: var(--md-on-surface);
}

.cost-divider {
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(76, 175, 80, 0.3), transparent);
    margin: 8px 0;
}

.cost-item.total {
    padding-top: 8px;
}

.cost-item.total .cost-label {
    font-size: 18px;
    font-weight: 700;
    color: var(--md-on-surface);
}

.cost-item.total .cost-value {
    font-size: 24px;
    font-weight: 800;
    color: var(--md-primary);
}

/* Touch-Friendly Action Buttons */
.action-buttons {
    display: flex;
    gap: 16px;
    margin-top: 32px;
    padding: 0 4px; /* Small padding for better visual spacing */
    max-width: 500px;
    margin-left: auto;
    margin-right: auto;
}

.button {
    flex: 1;
    padding: 18px 24px; /* Increased padding for touch */
    border-radius: 12px;
    font-size: 16px;
    font-weight: 600;
    font-family: var(--font-family);
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    /* Touch improvements */
    min-height: 56px;
    touch-action: manipulation;
    -webkit-tap-highlight-color: transparent;
}

.button.outlined {
    background: transparent;
    border: 2px solid rgba(76, 175, 80, 0.3);
    color: var(--md-primary);
}

.button.outlined:hover {
    background: rgba(76, 175, 80, 0.08);
    border-color: var(--md-primary);
    transform: translateY(-1px);
}

.button.contained {
    background: linear-gradient(135deg, var(--md-primary) 0%, var(--md-primary-dark) 100%);
    border: none;
    color: white;
    box-shadow: 0 4px 8px rgba(76, 175, 80, 0.3);
}

.button.contained:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 16px rgba(76, 175, 80, 0.4);
}

/* Touch-Friendly Snackbar */
.snackbar {
    padding: 16px 24px; /* Increased padding */
    border-radius: 12px;
    background: linear-gradient(135deg, #2d3748 0%, #1a202c 100%);
    color: white;
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.3);
    /* Touch improvements */
    min-height: 56px;
    display: flex;
    align-items: center;
    gap: 16px;
}

.snackbar-action {
    background: transparent;
    border: none;
    color: var(--md-primary);
    font-weight: 600;
    padding: 8px 16px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    /* Touch improvements */
    min-height: 40px;
    touch-action: manipulation;
    -webkit-tap-highlight-color: transparent;
}

.snackbar-action:hover {
    background: rgba(76, 175, 80, 0.1);
}

/* Responsive Design for Booking Details - Updated for Touch */
@media (max-width: 480px) {
    .booking-details-container,
    .tee-time-summary,
    .players-section,
    .special-requests,
    .cost-breakdown,
    .action-buttons {
        max-width: 380px; /* Narrower on mobile */
    }
    
    .summary-header {
        padding: 20px 16px; /* Increased vertical padding */
        gap: 16px; /* Increased gap */
    }
    
    .summary-icon {
        width: 44px; /* Slightly larger */
        height: 44px;
    }
    
    .summary-icon .material-icons {
        font-size: 22px; /* Slightly larger */
    }
    
    .summary-info h3 {
        font-size: 16px;
    }
    
    .player-header {
        padding: 20px 16px 16px; /* Increased vertical padding */
        gap: 16px; /* Increased gap */
    }
    
    .player-avatar {
        width: 44px; /* Slightly larger */
        height: 44px;
    }
    
    .player-avatar .material-icons {
        font-size: 22px; /* Slightly larger */
    }
    
    .player-details {
        padding: 20px 16px 24px; /* Increased padding */
        gap: 20px; /* Increased gap */
    }
    
    .special-requests,
    .cost-breakdown {
        padding: 24px 16px; /* Increased vertical padding */
    }
    
    .cost-item.total .cost-value {
        font-size: 20px;
    }
    
    .action-buttons {
        flex-direction: column; /* Stack buttons on mobile */
        gap: 12px;
    }
    
    .button {
        padding: 20px 24px; /* Larger touch targets on mobile */
        min-height: 60px;
    }
}

/* Dark mode support for booking details */
@media (prefers-color-scheme: dark) {
    .player-header {
        background: linear-gradient(135deg, rgba(76, 175, 80, 0.1) 0%, rgba(76, 175, 80, 0.05) 100%);
        border-bottom-color: rgba(76, 175, 80, 0.2);
    }
    
    .add-player-button {
        border-color: rgba(76, 175, 80, 0.4);
    }
    
    .add-player-button:hover {
        background: rgba(76, 175, 80, 0.1);
    }
}

/* Enhanced Phone Number Screen Styles */
.phone-screen-content {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 100vh;
    padding: 40px 20px;
}

.phone-container {
    width: 100%;
    max-width: 400px;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 24px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 
        0 20px 40px rgba(0, 0, 0, 0.1),
        0 1px 0 rgba(255, 255, 255, 0.5) inset;
    padding: 40px 32px;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.phone-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--md-primary) 0%, var(--md-primary-light) 50%, var(--md-primary) 100%);
}

/* Phone Header */
.phone-header {
    margin-bottom: 40px;
}

.phone-title {
    font-size: 28px;
    font-weight: 700;
    color: var(--md-on-surface);
    margin: 0 0 12px 0;
    line-height: 1.3;
}

.phone-subtitle {
    font-size: 16px;
    color: var(--md-on-surface-variant);
    margin: 0;
    line-height: 1.5;
    opacity: 0.8;
}

/* Enhanced Phone Input */
.phone-input-section {
    margin-bottom: 40px;
}

.enhanced-phone-input {
    display: flex;
    background: rgba(255, 255, 255, 0.9);
    border: 2px solid rgba(76, 175, 80, 0.2);
    border-radius: 16px;
    overflow: hidden;
    transition: all 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
    margin-bottom: 20px; /* Increased margin */
    /* Touch improvements */
    min-height: 64px; /* Minimum touch target height */
    touch-action: manipulation;
}

.enhanced-phone-input:focus-within {
    border-color: var(--md-primary);
    box-shadow: 0 0 0 4px rgba(76, 175, 80, 0.1);
    background: rgba(255, 255, 255, 0.95);
}

.country-selector {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 20px 24px; /* Increased padding for better touch target */
    background: rgba(248, 250, 252, 0.8);
    border-right: 1px solid rgba(76, 175, 80, 0.2);
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 120px; /* Increased minimum width */
    /* Touch improvements */
    touch-action: manipulation;
    -webkit-tap-highlight-color: transparent;
}

.country-selector:hover {
    background: rgba(76, 175, 80, 0.05);
}

.country-flag {
    width: 24px;
    height: auto;
    border-radius: 2px;
    flex-shrink: 0;
}

.country-code {
    font-size: 16px;
    font-weight: 600;
    color: var(--md-on-surface);
    margin-right: 4px;
}

.country-selector .material-icons {
    color: var(--md-on-surface-variant);
    font-size: 20px;
}

.phone-input-wrapper {
    flex: 1;
    position: relative;
}

.phone-number-input {
    width: 100%;
    padding: 20px 24px; /* Increased padding for better touch target */
    border: none;
    background: transparent;
    font-size: 16px; /* Minimum 16px to prevent zoom on iOS */
    font-family: var(--font-family);
    color: var(--md-on-surface);
    outline: none;
    font-weight: 500;
    /* Touch improvements */
    touch-action: manipulation;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
}

.phone-number-input::placeholder {
    color: var(--md-on-surface-variant);
    opacity: 0.6;
}

/* Input Helper Text */
.input-helper-text {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
    font-size: 14px;
    color: var(--md-on-surface-variant);
    margin: 0;
    opacity: 0.7;
}

.input-helper-text .material-icons {
    font-size: 16px;
    color: var(--md-primary);
}

/* Phone Actions */
.phone-actions {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 32px;
}

.phone-submit-btn {
    min-width: 180px;
    padding: 18px 32px;
    font-size: 16px;
    position: relative;
    overflow: hidden;
}

.phone-submit-btn::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: width 0.6s ease, height 0.6s ease;
}

.phone-submit-btn:hover::before {
    width: 300px;
    height: 300px;
}

/* Loading State for Phone Submit */
.phone-submit-btn.loading {
    pointer-events: none;
}

.phone-submit-btn.loading span {
    opacity: 0;
}

.phone-submit-btn.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    border: 2px solid transparent;
    border-top-color: white;
    border-radius: 50%;
    transform: translate(-50%, -50%);
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: translate(-50%, -50%) rotate(0deg); }
    100% { transform: translate(-50%, -50%) rotate(360deg); }
}

/* Success Animation */
.phone-container.success {
    animation: phoneSuccess 0.6s ease-out;
}

@keyframes phoneSuccess {
    0% { transform: scale(1); }
    50% { transform: scale(1.02); }
    100% { transform: scale(1); }
}

/* Responsive Design for Phone Screen */
@media (max-width: 480px) {
    .phone-screen-content {
        padding: 20px 16px;
        align-items: flex-start;
        padding-top: 80px;
    }
    
    .phone-container {
        max-width: 100%;
        padding: 32px 24px;
        border-radius: 20px;
    }
    
    .phone-title {
        font-size: 24px;
    }
    
    .phone-subtitle {
        font-size: 15px;
    }
    
    .enhanced-phone-input {
        border-radius: 12px;
    }
    
    .country-selector {
        padding: 14px 16px;
        min-width: 90px;
    }
    
    .phone-number-input {
        padding: 14px 16px;
        font-size: 16px;
    }
    
    .phone-submit-btn {
        min-width: 160px;
        padding: 16px 28px;
    }
}

@media (max-width: 320px) {
    .booking-details-container,
    .tee-time-summary,
    .players-section,
    .special-requests,
    .cost-breakdown,
    .action-buttons {
        max-width: 300px; /* Even narrower on very small screens */
    }
    
    .phone-container {
        padding: 28px 20px;
    }
    
    .country-selector {
        padding: 12px 14px;
        min-width: 85px;
    }
    
    .country-code {
        font-size: 15px;
    }
    
    .phone-number-input {
        padding: 12px 14px;
        font-size: 15px;
    }
}

/* Dark mode support for phone screen */
@media (prefers-color-scheme: dark) {
    .phone-container {
        background: linear-gradient(135deg, rgba(30, 41, 59, 0.95) 0%, rgba(15, 23, 42, 0.95) 100%);
        border-color: rgba(76, 175, 80, 0.3);
    }
    
    .enhanced-phone-input {
        background: rgba(30, 41, 59, 0.8);
        border-color: rgba(76, 175, 80, 0.3);
    }
    
    .enhanced-phone-input:focus-within {
        background: rgba(30, 41, 59, 0.95);
    }
    
    .country-selector {
        background: rgba(51, 65, 85, 0.8);
        border-right-color: rgba(76, 175, 80, 0.3);
    }
    
    .country-selector:hover {
        background: rgba(76, 175, 80, 0.1);
    }
    
    .phone-number-input {
        color: #e2e8f0;
    }
    
    .phone-number-input::placeholder {
        color: #94a3b8;
    }
}

/* Edit Dialog Styles */
.edit-dialog-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.6);
    backdrop-filter: blur(8px);
    z-index: 2000;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
}

.edit-dialog-overlay.show {
    opacity: 1;
    visibility: visible;
}

.edit-dialog {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.98) 0%, rgba(248, 250, 252, 0.98) 100%);
    border: 1px solid rgba(76, 175, 80, 0.2);
    border-radius: 24px;
    box-shadow: 
        0 24px 48px rgba(0, 0, 0, 0.15),
        0 1px 0 rgba(255, 255, 255, 0.5) inset;
    backdrop-filter: blur(20px);
    max-width: 480px;
    width: 100%;
    max-height: 90vh;
    overflow-y: auto;
    transform: scale(0.9) translateY(20px);
    transition: all 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
}

.edit-dialog-overlay.show .edit-dialog {
    transform: scale(1) translateY(0);
}

.edit-dialog-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 24px 32px 16px;
    border-bottom: 1px solid rgba(76, 175, 80, 0.1);
}

.edit-dialog-header h3 {
    font-size: 20px;
    font-weight: 600;
    color: var(--md-on-surface);
    margin: 0;
}

.close-dialog-btn {
    width: 40px;
    height: 40px;
    background: transparent;
    border: none;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    color: var(--md-on-surface-variant);
    /* Touch improvements */
    touch-action: manipulation;
    -webkit-tap-highlight-color: transparent;
}

.close-dialog-btn:hover {
    background: rgba(76, 175, 80, 0.1);
    color: var(--md-primary);
}

.close-dialog-btn .material-icons {
    font-size: 20px;
}

.edit-dialog-content {
    padding: 24px 32px;
}

.edit-section {
    margin-bottom: 32px;
}

.edit-section:last-child {
    margin-bottom: 0;
}

.edit-label {
    display: block;
    font-size: 16px;
    font-weight: 600;
    color: var(--md-on-surface);
    margin-bottom: 12px;
}

.edit-options {
    display: flex;
    gap: 12px;
    flex-wrap: wrap;
}

.edit-option-btn {
    flex: 1;
    min-width: 120px;
    padding: 16px 20px;
    background: rgba(255, 255, 255, 0.8);
    border: 2px solid rgba(76, 175, 80, 0.2);
    border-radius: 12px;
    color: var(--md-on-surface);
    font-size: 14px;
    font-weight: 600;
    font-family: var(--font-family);
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
    text-align: center;
    /* Touch improvements */
    min-height: 52px;
    touch-action: manipulation;
    -webkit-tap-highlight-color: transparent;
}

.edit-option-btn:hover {
    background: rgba(76, 175, 80, 0.05);
    border-color: var(--md-primary);
    transform: translateY(-1px);
}

.edit-option-btn.selected {
    background: var(--md-primary);
    border-color: var(--md-primary);
    color: white;
    box-shadow: 0 4px 8px rgba(76, 175, 80, 0.3);
}

.edit-option-btn:active {
    transform: scale(0.98);
}

.edit-dialog-actions {
    display: flex;
    gap: 16px;
    padding: 16px 32px 32px;
    border-top: 1px solid rgba(76, 175, 80, 0.1);
}

.edit-dialog-actions .button {
    flex: 1;
    min-height: 52px;
    font-size: 16px;
}

/* Responsive Design for Edit Dialog */
@media (max-width: 480px) {
    .edit-dialog-overlay {
        padding: 16px;
    }
    
    .edit-dialog {
        border-radius: 20px;
        max-height: 95vh;
    }
    
    .edit-dialog-header {
        padding: 20px 24px 16px;
    }
    
    .edit-dialog-header h3 {
        font-size: 18px;
    }
    
    .edit-dialog-content {
        padding: 20px 24px;
    }
    
    .edit-section {
        margin-bottom: 28px;
    }
    
    .edit-options {
        flex-direction: column;
        gap: 8px;
    }
    
    .edit-option-btn {
        min-width: auto;
        flex: none;
    }
    
    .edit-dialog-actions {
        padding: 16px 24px 24px;
        flex-direction: column;
    }
}

/* Dark mode support for edit dialog */
@media (prefers-color-scheme: dark) {
    .edit-dialog {
        background: linear-gradient(135deg, rgba(30, 41, 59, 0.98) 0%, rgba(15, 23, 42, 0.98) 100%);
        border-color: rgba(76, 175, 80, 0.3);
    }
    
    .edit-dialog-header {
        border-bottom-color: rgba(76, 175, 80, 0.2);
    }
    
    .edit-option-btn {
        background: rgba(30, 41, 59, 0.8);
        border-color: rgba(76, 175, 80, 0.3);
        color: #e2e8f0;
    }
    
    .edit-option-btn:hover {
        background: rgba(76, 175, 80, 0.1);
    }
    
    .edit-option-btn.selected {
        background: var(--md-primary);
        color: white;
    }
    
    .edit-dialog-actions {
        border-top-color: rgba(76, 175, 80, 0.2);
    }
}

/* Modern Booking Details Screen Styles */

/* Modern Booking Header */
.modern-booking-header {
    margin-bottom: 24px;
    max-width: 500px;
    margin-left: auto;
    margin-right: auto;
}

.booking-info-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 16px;
    padding: 20px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.booking-info-row {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.booking-info-item {
    display: flex;
    align-items: center;
    gap: 12px;
}

.booking-icon {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, var(--md-primary), var(--md-primary-light));
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.booking-icon .material-icons {
    color: white;
    font-size: 20px;
}

.booking-text {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.booking-label {
    font-size: 12px;
    font-weight: 500;
    color: var(--md-on-surface-variant);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.booking-value {
    font-size: 16px;
    font-weight: 600;
    color: var(--md-on-surface);
}

/* Modern Players Section */
.modern-players-section {
    margin-bottom: 24px;
    max-width: 500px;
    margin-left: auto;
    margin-right: auto;
}

.section-header {
    margin-bottom: 16px;
}

.section-title {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 20px;
    font-weight: 700;
    color: white;
    margin: 0;
}

.section-title .material-icons {
    font-size: 24px;
}

.modern-player-cards {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

/* Modern Player Card */
.modern-player-card {
    position: relative;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
}

.modern-player-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.player-card-border {
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 4px;
    background: linear-gradient(135deg, var(--md-primary), var(--md-primary-light));
}

.player-card-content {
    padding: 20px;
}

.player-main-info {
    display: flex;
    align-items: center;
    gap: 16px;
    margin-bottom: 16px;
}

.player-avatar-modern {
    width: 48px;
    height: 48px;
    background: linear-gradient(135deg, var(--md-primary), var(--md-primary-light));
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.player-avatar-modern .material-icons {
    color: white;
    font-size: 24px;
}

.player-details-modern {
    flex: 1;
}

.player-name {
    font-size: 18px;
    font-weight: 600;
    color: var(--md-on-surface);
    margin: 0 0 4px 0;
}

.player-role {
    font-size: 14px;
    color: var(--md-on-surface-variant);
    margin: 0;
}

.player-price-modern {
    text-align: right;
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.price-amount {
    font-size: 20px;
    font-weight: 700;
    color: var(--md-primary);
}

.price-label {
    font-size: 12px;
    color: var(--md-on-surface-variant);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.player-features {
    display: flex;
    gap: 12px;
    flex-wrap: wrap;
}

.feature-tag {
    display: flex;
    align-items: center;
    gap: 6px;
    background: rgba(76, 175, 80, 0.1);
    color: var(--md-primary);
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 500;
}

.feature-tag .material-icons {
    font-size: 16px;
}

/* Modern Summary Card */
.modern-summary-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 16px;
    padding: 20px;
    margin-bottom: 24px;
    max-width: 500px;
    margin-left: auto;
    margin-right: auto;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.summary-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    padding-bottom: 12px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.summary-header h3 {
    font-size: 18px;
    font-weight: 600;
    color: var(--md-on-surface);
    margin: 0;
}

.summary-badge {
    display: flex;
    align-items: center;
    gap: 4px;
    background: rgba(76, 175, 80, 0.1);
    color: var(--md-primary);
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

.summary-badge .material-icons {
    font-size: 16px;
}

.summary-breakdown {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.summary-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 14px;
    color: var(--md-on-surface-variant);
}

.summary-divider {
    height: 1px;
    background: rgba(0, 0, 0, 0.1);
    margin: 8px 0;
}

.summary-total {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 18px;
    font-weight: 700;
    color: var(--md-on-surface);
    padding-top: 8px;
}

/* Modern Proceed Button */
.modern-proceed-button {
    position: relative;
    width: 100%;
    max-width: 500px;
    margin: 0 auto;
    padding: 0;
    background: linear-gradient(135deg, var(--md-primary), var(--md-primary-dark));
    border: none;
    border-radius: 16px;
    overflow: hidden;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
    box-shadow: 0 8px 32px rgba(76, 175, 80, 0.25);
}

.modern-proceed-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 40px rgba(76, 175, 80, 0.35);
}

.modern-proceed-button:active {
    transform: translateY(0);
}

.button-content {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
    padding: 18px 24px;
    color: white;
    font-size: 16px;
    font-weight: 600;
    position: relative;
    z-index: 2;
}

.button-content .material-icons {
    font-size: 20px;
}

.button-shine {
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.modern-proceed-button:hover .button-shine {
    left: 100%;
}

/* Responsive Design for Modern Booking Details */
@media (max-width: 480px) {
    .booking-info-row {
        gap: 12px;
    }
    
    .booking-info-item {
        gap: 10px;
    }
    
    .booking-icon {
        width: 36px;
        height: 36px;
    }
    
    .booking-icon .material-icons {
        font-size: 18px;
    }
    
    .booking-value {
        font-size: 14px;
    }
    
    .modern-player-card {
        margin: 0 8px;
    }
    
    .player-card-content {
        padding: 16px;
    }
    
    .player-main-info {
        gap: 12px;
        margin-bottom: 12px;
    }
    
    .player-avatar-modern {
        width: 40px;
        height: 40px;
    }
    
    .player-avatar-modern .material-icons {
        font-size: 20px;
    }
    
    .player-name {
        font-size: 16px;
    }
    
    .price-amount {
        font-size: 18px;
    }
    
    .feature-tag {
        gap: 4px;
        padding: 4px 8px;
        font-size: 11px;
    }
    
    .feature-tag .material-icons {
        font-size: 14px;
    }
    
    .modern-summary-card {
        margin: 0 8px 24px;
        padding: 16px;
    }
    
    .button-content {
        padding: 16px 20px;
        font-size: 15px;
    }
}

/* Dark mode support for modern booking details */
@media (prefers-color-scheme: dark) {
    .booking-info-card,
    .modern-player-card,
    .modern-summary-card {
        background: rgba(30, 30, 30, 0.95);
        border: 1px solid rgba(255, 255, 255, 0.1);
    }
    
    .booking-value,
    .player-name,
    .summary-header h3,
    .summary-total {
        color: white;
    }
    
    .booking-label,
    .player-role,
    .summary-item {
        color: rgba(255, 255, 255, 0.7);
    }
    
    .summary-header,
    .summary-divider {
        border-color: rgba(255, 255, 255, 0.1);
    }
}